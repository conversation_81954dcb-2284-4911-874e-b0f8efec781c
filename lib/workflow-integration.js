import { createDeepSiteWorkflow, createPromptEnhancementWorkflow, DeepSiteState } from './langgraph-workflows.js';
import { DocumentationMCPServer } from './mcp-server.js';

/**
 * Workflow Integration Layer
 * Bridges the existing server API with LangGraph workflows
 */
export class WorkflowIntegration {
  constructor() {
    this.mainWorkflow = null;
    this.enhancementWorkflow = null;
    this.mcpServer = null;
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Initialize workflows
      this.mainWorkflow = createDeepSiteWorkflow();
      this.enhancementWorkflow = createPromptEnhancementWorkflow();
      
      // Initialize MCP server (optional for now)
      try {
        this.mcpServer = new DocumentationMCPServer();
        console.log("MCP Documentation Server initialized");
      } catch (error) {
        console.warn("MCP Server initialization failed, continuing without it:", error.message);
      }
      
      this.initialized = true;
      console.log("Workflow integration initialized successfully");
    } catch (error) {
      console.error("Failed to initialize workflow integration:", error);
      throw error;
    }
  }

  /**
   * Process initial AI request using LangGraph workflow
   */
  async processInitialRequest({
    prompt,
    provider,
    model,
    redesignMarkdown,
    openrouterApiKey,
    customModel,
    maxTokens = 4000,
    temperature = 0.7
  }) {
    await this.initialize();

    const apiKeys = {};
    if (openrouterApiKey) apiKeys.openrouter = openrouterApiKey;
    if (process.env.GOOGLE_API_KEY) apiKeys.google = process.env.GOOGLE_API_KEY;
    if (process.env.OPENAI_API_KEY) apiKeys.openai = process.env.OPENAI_API_KEY;

    const initialState = {
      messages: [{
        role: "user",
        content: redesignMarkdown 
          ? `Here is my current design as a markdown:\n\n${redesignMarkdown}\n\nNow, please create a new design based on this markdown.`
          : prompt
      }],
      provider,
      model,
      apiKeys,
      customModel,
      maxTokens,
      temperature,
      codeType: "html",
      isFollowUp: false,
      redesignMarkdown
    };

    try {
      const result = await this.mainWorkflow.invoke(initialState);
      const lastMessage = result.messages[result.messages.length - 1];
      
      return {
        content: typeof lastMessage.content === 'string' ? lastMessage.content : '',
        documentation: result.documentation || '',
        context: result.context || ''
      };
    } catch (error) {
      console.error("Workflow execution error:", error);
      throw new Error(`Workflow execution failed: ${error.message}`);
    }
  }

  /**
   * Process follow-up request using LangGraph workflow
   */
  async processFollowUpRequest({
    prompt,
    html,
    previousPrompt,
    provider,
    model,
    openrouterApiKey,
    customModel,
    maxTokens = 4000,
    temperature = 0.7
  }) {
    await this.initialize();

    const apiKeys = {};
    if (openrouterApiKey) apiKeys.openrouter = openrouterApiKey;
    if (process.env.GOOGLE_API_KEY) apiKeys.google = process.env.GOOGLE_API_KEY;
    if (process.env.OPENAI_API_KEY) apiKeys.openai = process.env.OPENAI_API_KEY;

    const initialState = {
      messages: [{
        role: "user",
        content: prompt
      }],
      provider,
      model,
      apiKeys,
      customModel,
      maxTokens,
      temperature,
      html,
      previousPrompt,
      isFollowUp: true
    };

    try {
      const result = await this.mainWorkflow.invoke(initialState);
      const lastMessage = result.messages[result.messages.length - 1];
      
      return {
        content: typeof lastMessage.content === 'string' ? lastMessage.content : '',
        documentation: result.documentation || '',
        context: result.context || ''
      };
    } catch (error) {
      console.error("Follow-up workflow execution error:", error);
      throw new Error(`Follow-up workflow execution failed: ${error.message}`);
    }
  }

  /**
   * Process prompt enhancement using LangGraph workflow
   */
  async processPromptEnhancement({
    prompt,
    provider = "google",
    model = "gemini-2.5-flash-preview-05-20",
    maxTokens = 500,
    temperature = 0.7
  }) {
    await this.initialize();

    const apiKeys = {};
    if (process.env.GOOGLE_API_KEY) apiKeys.google = process.env.GOOGLE_API_KEY;
    if (process.env.OPENAI_API_KEY) apiKeys.openai = process.env.OPENAI_API_KEY;

    const initialState = {
      messages: [{
        role: "user",
        content: prompt
      }],
      provider,
      model,
      apiKeys,
      maxTokens,
      temperature
    };

    try {
      const result = await this.enhancementWorkflow.invoke(initialState);
      const lastMessage = result.messages[result.messages.length - 1];
      
      return {
        enhancedPrompt: typeof lastMessage.content === 'string' ? lastMessage.content : prompt
      };
    } catch (error) {
      console.error("Prompt enhancement workflow error:", error);
      throw new Error(`Prompt enhancement failed: ${error.message}`);
    }
  }

  /**
   * Stream initial request (for compatibility with existing streaming API)
   */
  async* streamInitialRequest(params) {
    try {
      const result = await this.processInitialRequest(params);
      
      // Simulate streaming by yielding chunks
      const content = result.content;
      const chunkSize = 50; // Characters per chunk
      
      for (let i = 0; i < content.length; i += chunkSize) {
        const chunk = content.slice(i, i + chunkSize);
        yield {
          choices: [{
            delta: {
              content: chunk
            }
          }]
        };
        
        // Add small delay to simulate real streaming
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    } catch (error) {
      console.error("Streaming error:", error);
      throw error;
    }
  }

  /**
   * Check if workflows are available
   */
  isAvailable() {
    return this.initialized && this.mainWorkflow && this.enhancementWorkflow;
  }

  /**
   * Get workflow status
   */
  getStatus() {
    return {
      initialized: this.initialized,
      mainWorkflow: !!this.mainWorkflow,
      enhancementWorkflow: !!this.enhancementWorkflow,
      mcpServer: !!this.mcpServer
    };
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    if (this.mcpServer) {
      await this.mcpServer.close();
    }
    this.initialized = false;
  }
}

// Create singleton instance
export const workflowIntegration = new WorkflowIntegration();

/**
 * Helper function to check if LangGraph should be used
 */
export function shouldUseLangGraph(provider, model) {
  // For now, enable LangGraph for all providers
  // Can be made configurable later
  return true;
}

/**
 * Helper function to extract search/replace blocks from LangGraph output
 */
export function parseSearchReplaceBlocks(content) {
  const SEARCH_START = "<<<<<<< SEARCH";
  const DIVIDER = "=======";
  const REPLACE_END = ">>>>>>> REPLACE";
  
  const blocks = [];
  let position = 0;
  
  while (true) {
    const searchStartIndex = content.indexOf(SEARCH_START, position);
    if (searchStartIndex === -1) break;
    
    const dividerIndex = content.indexOf(DIVIDER, searchStartIndex);
    if (dividerIndex === -1) break;
    
    const replaceEndIndex = content.indexOf(REPLACE_END, dividerIndex);
    if (replaceEndIndex === -1) break;
    
    const searchBlock = content.substring(
      searchStartIndex + SEARCH_START.length,
      dividerIndex
    ).trim();
    
    const replaceBlock = content.substring(
      dividerIndex + DIVIDER.length,
      replaceEndIndex
    ).trim();
    
    blocks.push({ search: searchBlock, replace: replaceBlock });
    position = replaceEndIndex + REPLACE_END.length;
  }
  
  return blocks;
}

/**
 * Apply search/replace blocks to HTML content
 */
export function applySearchReplaceBlocks(html, blocks) {
  let newHtml = html;
  const updatedLines = [];
  
  for (const block of blocks) {
    const { search, replace } = block;
    
    if (search === "") {
      // Inserting at the beginning
      newHtml = `${replace}\n${newHtml}`;
      updatedLines.push([1, replace.split("\n").length]);
    } else {
      // Find and replace
      const blockPosition = newHtml.indexOf(search);
      if (blockPosition !== -1) {
        const beforeText = newHtml.substring(0, blockPosition);
        const startLineNumber = beforeText.split("\n").length;
        const replaceLines = replace.split("\n").length;
        const endLineNumber = startLineNumber + replaceLines - 1;
        
        updatedLines.push([startLineNumber, endLineNumber]);
        newHtml = newHtml.replace(search, replace);
      }
    }
  }
  
  return { html: newHtml, updatedLines };
}
