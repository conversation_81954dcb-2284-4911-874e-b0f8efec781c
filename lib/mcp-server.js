import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

/**
 * MCP Documentation Server
 * Provides tools and resources for fetching documentation and context
 */
export class DocumentationMCPServer {
  constructor() {
    this.server = new McpServer({
      name: "DeepSite Documentation Server",
      version: "1.0.0"
    });
    
    this.setupTools();
    this.setupResources();
  }

  setupTools() {
    // Tool for fetching API documentation
    this.server.tool(
      "fetch_api_docs",
      {
        library: z.string().describe("Library or API name to fetch documentation for"),
        topic: z.string().optional().describe("Specific topic or section to focus on"),
        format: z.enum(["markdown", "json", "text"]).default("markdown").describe("Output format")
      },
      async ({ library, topic, format }) => {
        try {
          const docs = await this.fetchDocumentation(library, topic);
          return {
            content: [{
              type: "text",
              text: this.formatDocumentation(docs, format)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Error fetching documentation: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );

    // Tool for analyzing code context
    this.server.tool(
      "analyze_code_context",
      {
        code: z.string().describe("Code to analyze"),
        language: z.string().default("javascript").describe("Programming language"),
        context_type: z.enum(["dependencies", "patterns", "best_practices"]).describe("Type of analysis")
      },
      async ({ code, language, context_type }) => {
        try {
          const analysis = await this.analyzeCode(code, language, context_type);
          return {
            content: [{
              type: "text",
              text: analysis
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Error analyzing code: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );

    // Tool for searching best practices
    this.server.tool(
      "search_best_practices",
      {
        technology: z.string().describe("Technology or framework"),
        use_case: z.string().describe("Specific use case or pattern"),
        difficulty: z.enum(["beginner", "intermediate", "advanced"]).default("intermediate")
      },
      async ({ technology, use_case, difficulty }) => {
        try {
          const practices = await this.searchBestPractices(technology, use_case, difficulty);
          return {
            content: [{
              type: "text",
              text: practices
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Error searching best practices: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );
  }

  setupResources() {
    // Dynamic resource for library documentation
    this.server.resource(
      "library-docs",
      new ResourceTemplate("docs://{library}/{version?}", { list: undefined }),
      async (uri, { library, version }) => {
        try {
          const docs = await this.fetchLibraryDocs(library, version);
          return {
            contents: [{
              uri: uri.href,
              text: docs
            }]
          };
        } catch (error) {
          return {
            contents: [{
              uri: uri.href,
              text: `Error: ${error.message}`
            }]
          };
        }
      }
    );

    // Static resource for common patterns
    this.server.resource(
      "common-patterns",
      "patterns://web-development",
      async (uri) => ({
        contents: [{
          uri: uri.href,
          text: this.getCommonPatterns()
        }]
      })
    );
  }

  async fetchDocumentation(library, topic) {
    // Simulate documentation fetching
    // In a real implementation, this would fetch from various sources:
    // - Official documentation APIs
    // - GitHub repositories
    // - Package registries
    // - Community resources
    
    const mockDocs = {
      "react": {
        "hooks": "React Hooks allow you to use state and other React features without writing a class...",
        "components": "Components let you split the UI into independent, reusable pieces...",
        "default": "React is a JavaScript library for building user interfaces..."
      },
      "express": {
        "routing": "Routing refers to how an application's endpoints respond to client requests...",
        "middleware": "Middleware functions are functions that have access to the request object...",
        "default": "Express is a minimal and flexible Node.js web application framework..."
      },
      "tailwindcss": {
        "utilities": "Tailwind CSS works by scanning all of your HTML files, JavaScript components...",
        "responsive": "Every utility class in Tailwind can be applied conditionally at different breakpoints...",
        "default": "Tailwind CSS is a utility-first CSS framework for rapidly building custom user interfaces..."
      }
    };

    const libraryDocs = mockDocs[library.toLowerCase()] || {};
    return libraryDocs[topic] || libraryDocs.default || `Documentation for ${library} not found.`;
  }

  async analyzeCode(code, language, contextType) {
    // Simulate code analysis
    // In a real implementation, this would use AST parsing, static analysis tools, etc.
    
    const patterns = {
      dependencies: `Analyzing dependencies in ${language} code:\n- Detected imports/requires\n- Version compatibility checks\n- Security considerations`,
      patterns: `Code patterns detected:\n- Design patterns used\n- Anti-patterns to avoid\n- Refactoring suggestions`,
      best_practices: `Best practices analysis:\n- Code style compliance\n- Performance considerations\n- Maintainability improvements`
    };

    return patterns[contextType] || "Analysis complete.";
  }

  async searchBestPractices(technology, useCase, difficulty) {
    // Simulate best practices search
    const practices = `Best practices for ${technology} - ${useCase} (${difficulty} level):
    
1. Follow established conventions and patterns
2. Implement proper error handling
3. Use appropriate design patterns
4. Optimize for performance and maintainability
5. Include comprehensive testing
6. Document your code effectively
7. Consider security implications
8. Plan for scalability`;

    return practices;
  }

  async fetchLibraryDocs(library, version) {
    // Simulate library documentation fetching
    return `Documentation for ${library}${version ? ` v${version}` : ''}:
    
## Overview
This library provides essential functionality for modern web development.

## Installation
\`\`\`bash
npm install ${library}
\`\`\`

## Basic Usage
\`\`\`javascript
import { ${library} } from '${library}';
\`\`\`

## API Reference
[Detailed API documentation would be here]
`;
  }

  formatDocumentation(docs, format) {
    switch (format) {
      case "json":
        return JSON.stringify({ documentation: docs }, null, 2);
      case "text":
        return docs.replace(/[#*`]/g, ''); // Remove markdown formatting
      case "markdown":
      default:
        return docs;
    }
  }

  getCommonPatterns() {
    return `# Common Web Development Patterns

## Component Patterns
- Container/Presentational Components
- Higher-Order Components (HOCs)
- Render Props
- Custom Hooks

## State Management
- Redux Pattern
- Context API
- Local State Management
- Global State Patterns

## API Patterns
- RESTful APIs
- GraphQL
- Real-time Communication
- Error Handling

## Performance Patterns
- Code Splitting
- Lazy Loading
- Memoization
- Virtualization
`;
  }

  async connect(transport) {
    await this.server.connect(transport);
  }

  async close() {
    // Cleanup if needed
  }
}

// Export a function to create and start the MCP server
export async function createDocumentationMCPServer() {
  const server = new DocumentationMCPServer();
  const transport = new StdioServerTransport();
  await server.connect(transport);
  return server;
}
