import { StateGraph, MessagesAnnotation, Annotation, START, END } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { AIMessage, HumanMessage, SystemMessage } from "@langchain/core/messages";

/**
 * Custom state for DeepSite workflows
 */
export const DeepSiteState = Annotation.Root({
  ...MessagesAnnotation.spec,
  provider: Annotation(),
  model: Annotation(),
  context: Annotation(),
  documentation: Annotation(),
  codeType: Annotation(),
  maxTokens: Annotation(),
  temperature: Annotation(),
  apiKeys: Annotation(),
  customModel: Annotation(),
  redesignMarkdown: Annotation(),
  previousPrompt: Annotation(),
  html: Annotation(),
  isFollowUp: Annotation(),
});

/**
 * Documentation fetching tool
 */
const fetchDocumentationTool = tool(
  async ({ library, topic, format }) => {
    // This would integrate with the MCP server
    // For now, we'll simulate the documentation fetching
    const mockDocs = {
      "tailwindcss": "Tailwind CSS is a utility-first CSS framework. Use classes like 'bg-blue-500', 'text-white', 'p-4' for styling.",
      "react": "React is a JavaScript library for building user interfaces. Use functional components with hooks.",
      "javascript": "Modern JavaScript features include arrow functions, destructuring, async/await, and modules.",
      "html": "HTML5 provides semantic elements like <header>, <nav>, <main>, <section>, <article>, <aside>, <footer>.",
      "css": "Modern CSS includes Flexbox, Grid, custom properties (variables), and responsive design principles."
    };
    
    const docs = mockDocs[library.toLowerCase()] || `Documentation for ${library} - ${topic || 'general usage'}`;
    return `Documentation: ${docs}`;
  },
  {
    name: "fetch_documentation",
    description: "Fetch documentation for libraries, frameworks, or technologies",
    schema: z.object({
      library: z.string().describe("Library or technology name"),
      topic: z.string().optional().describe("Specific topic or feature"),
      format: z.string().optional().describe("Output format preference")
    })
  }
);

/**
 * Code analysis tool
 */
const analyzeCodeTool = tool(
  async ({ code, analysisType }) => {
    // Simulate code analysis
    const analysis = {
      dependencies: "Detected dependencies: React, Tailwind CSS, modern JavaScript features",
      patterns: "Code follows modern component patterns with functional programming principles",
      best_practices: "Code adheres to best practices: semantic HTML, responsive design, accessibility considerations",
      security: "No security issues detected in the provided code"
    };
    
    return analysis[analysisType] || "Code analysis completed";
  },
  {
    name: "analyze_code",
    description: "Analyze code for patterns, dependencies, and best practices",
    schema: z.object({
      code: z.string().describe("Code to analyze"),
      analysisType: z.enum(["dependencies", "patterns", "best_practices", "security"]).describe("Type of analysis")
    })
  }
);

/**
 * Create a chat model based on provider and configuration
 */
function createChatModel(state) {
  const { provider, model, apiKeys, customModel, maxTokens = 4000, temperature = 0.7 } = state;

  if (provider === "openrouter" && apiKeys?.openrouter) {
    return new ChatOpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: apiKeys.openrouter,
      modelName: model === "custom" && customModel ? customModel : model,
      maxTokens: Math.min(maxTokens, 4000),
      temperature
    });
  }

  if (provider === "google" && apiKeys?.google) {
    // For Google models, we'll need to handle this differently
    // as GoogleGenerativeAI doesn't follow the same interface
    // For now, we'll simulate the response
    return {
      invoke: async (messages) => {
        const googleAI = new GoogleGenerativeAI(apiKeys.google);
        const genModel = googleAI.getGenerativeModel({ model: model || "gemini-1.5-flash" });

        // Convert messages to Google format
        const systemMessage = messages.find(m => m.role === 'system' || m._getType() === 'system');
        const userMessages = messages.filter(m => m.role === 'user' || m.role === 'assistant' || m._getType() === 'user' || m._getType() === 'ai');

        let prompt = '';
        if (systemMessage) {
          prompt += `${systemMessage.content}\n\n`;
        }

        // Combine user and assistant messages into a conversation
        userMessages.forEach(msg => {
          const content = typeof msg.content === 'string' ? msg.content : msg.content?.text || '';
          if (msg.role === 'user' || msg._getType() === 'user') {
            prompt += `User: ${content}\n`;
          } else if (msg.role === 'assistant' || msg._getType() === 'ai') {
            prompt += `Assistant: ${content}\n`;
          }
        });

        const result = await genModel.generateContent(prompt);
        const response = await result.response;

        return {
          content: response.text(),
          _getType: () => 'ai'
        };
      }
    };
  }

  // Default to OpenAI if API key is available
  if (apiKeys?.openai) {
    return new ChatOpenAI({
      apiKey: apiKeys.openai,
      modelName: model || "gpt-4o",
      maxTokens: Math.min(maxTokens, 4000),
      temperature
    });
  }

  // If no API keys available, return a mock model for testing
  return {
    invoke: async (messages) => {
      const lastMessage = messages[messages.length - 1];
      const prompt = typeof lastMessage.content === 'string' ? lastMessage.content : '';

      // Generate a simple HTML response for testing
      const mockResponse = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello World</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <div class="bg-white p-8 rounded-lg shadow-md">
        <h1 class="text-3xl font-bold text-blue-600 mb-4">Hello World!</h1>
        <p class="text-gray-700">This is a simple HTML page created with Tailwind CSS.</p>
        <p class="text-sm text-gray-500 mt-4">Generated in response to: "${prompt.substring(0, 50)}..."</p>
    </div>
</body>
</html>`;

      return {
        content: mockResponse,
        _getType: () => 'ai'
      };
    }
  };
}

/**
 * Documentation gathering node
 */
async function gatherDocumentation(state) {
  console.log("---GATHERING DOCUMENTATION---");
  
  const { messages, codeType = "html" } = state;
  const userMessage = messages[messages.length - 1];
  const prompt = typeof userMessage.content === 'string' ? userMessage.content : '';
  
  // Extract technologies mentioned in the prompt
  const technologies = extractTechnologies(prompt);
  
  let documentation = "";
  for (const tech of technologies) {
    try {
      const docs = await fetchDocumentationTool.invoke({ library: tech });
      documentation += `\n${docs}`;
    } catch (error) {
      console.error(`Error fetching docs for ${tech}:`, error);
    }
  }
  
  return {
    documentation: documentation.trim(),
    context: `Building ${codeType.toUpperCase()} with: ${technologies.join(", ")}`
  };
}

/**
 * Code generation node
 */
async function generateCode(state) {
  console.log("---GENERATING CODE---");
  
  const { messages, documentation, context, isFollowUp, html, previousPrompt } = state;
  const model = createChatModel(state);
  
  let systemPrompt;
  let userPrompt;
  
  if (isFollowUp && html) {
    // Follow-up modification
    systemPrompt = `You are an expert web developer modifying an existing HTML file.
The user wants to apply changes based on their request.
You MUST output ONLY the changes required using the following SEARCH/REPLACE block format.

${documentation ? `\nRelevant Documentation:\n${documentation}\n` : ''}

Format Rules:
1. Start with <<<<<<< SEARCH
2. Provide the exact lines from the current code that need to be replaced.
3. Use ======= to separate the search block from the replacement.
4. Provide the new lines that should replace the original lines.
5. End with >>>>>>> REPLACE
6. You can use multiple SEARCH/REPLACE blocks if changes are needed in different parts of the file.`;

    const lastMessage = messages[messages.length - 1];
    const modificationRequest = typeof lastMessage.content === 'string' ? lastMessage.content : '';
    
    userPrompt = `Current HTML code:
\`\`\`html
${html}
\`\`\`

Previous request: ${previousPrompt || 'Initial creation'}

New modification request: ${modificationRequest}

Please provide the necessary changes using the SEARCH/REPLACE format.`;
  } else {
    // Initial generation
    systemPrompt = `ONLY USE HTML, CSS AND JAVASCRIPT. If you want to use ICON make sure to import the library first. Try to create the best UI possible by using only HTML, CSS and JAVASCRIPT. MAKE IT RESPONSIVE USING TAILWINDCSS. Use as much as you can TailwindCSS for the CSS, if you can't do something with TailwindCSS, then use custom CSS (make sure to import <script src="https://cdn.tailwindcss.com"></script> in the head). Also, try to elaborate as much as you can, to create something unique. ALWAYS GIVE THE RESPONSE INTO A SINGLE HTML FILE.

${documentation ? `\nRelevant Documentation:\n${documentation}\n` : ''}
${context ? `\nContext: ${context}\n` : ''}`;

    const lastMessage = messages[messages.length - 1];
    userPrompt = typeof lastMessage.content === 'string' ? lastMessage.content : '';
  }
  
  const response = await model.invoke([
    new SystemMessage(systemPrompt),
    new HumanMessage(userPrompt)
  ]);
  
  return {
    messages: [...messages, response]
  };
}

/**
 * Extract technologies from prompt
 */
function extractTechnologies(prompt) {
  const technologies = [];
  const techKeywords = {
    'tailwind': 'tailwindcss',
    'tailwindcss': 'tailwindcss',
    'react': 'react',
    'javascript': 'javascript',
    'js': 'javascript',
    'html': 'html',
    'css': 'css',
    'bootstrap': 'bootstrap',
    'vue': 'vue',
    'angular': 'angular'
  };
  
  const lowerPrompt = prompt.toLowerCase();
  for (const [keyword, tech] of Object.entries(techKeywords)) {
    if (lowerPrompt.includes(keyword) && !technologies.includes(tech)) {
      technologies.push(tech);
    }
  }
  
  // Default technologies for web development
  if (technologies.length === 0) {
    technologies.push('html', 'css', 'javascript', 'tailwindcss');
  }
  
  return technologies;
}

/**
 * Routing logic
 */
function shouldGatherDocs(state) {
  const { documentation, isFollowUp } = state;
  
  // Skip documentation gathering for follow-up requests if we already have docs
  if (isFollowUp && documentation) {
    return "generate";
  }
  
  return "docs";
}

/**
 * Create the main workflow graph
 */
export function createDeepSiteWorkflow() {
  const workflow = new StateGraph(DeepSiteState)
    .addNode("docs", gatherDocumentation)
    .addNode("generate", generateCode)
    .addEdge(START, "docs")
    .addEdge("docs", "generate")
    .addEdge("generate", END)
    .addConditionalEdges(START, shouldGatherDocs, {
      docs: "docs",
      generate: "generate"
    });

  return workflow.compile();
}

/**
 * Create a simplified workflow for prompt enhancement
 */
export function createPromptEnhancementWorkflow() {
  const enhancePrompt = async (state) => {
    console.log("---ENHANCING PROMPT---");
    
    const { messages } = state;
    const model = createChatModel(state);
    
    const systemPrompt = `You are a prompt enhancement assistant. Your job is to improve user prompts to make them more specific, clear, and effective for AI code generation.

Guidelines for enhancement:
1. Make prompts more specific and detailed
2. Add context about the desired outcome
3. Include relevant technical details when appropriate
4. Maintain the original intent while improving clarity
5. Keep the enhanced prompt concise but comprehensive
6. Focus on actionable instructions

Return only the enhanced prompt without any additional explanation or formatting.`;

    const userMessage = messages[messages.length - 1];
    const originalPrompt = typeof userMessage.content === 'string' ? userMessage.content : '';
    
    const userPrompt = `Please enhance this prompt for better AI code generation results:

"${originalPrompt.trim()}"`;

    const response = await model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage(userPrompt)
    ]);
    
    return {
      messages: [...messages, response]
    };
  };
  
  const workflow = new StateGraph(DeepSiteState)
    .addNode("enhance", enhancePrompt)
    .addEdge(START, "enhance")
    .addEdge("enhance", END);
  
  return workflow.compile();
}
